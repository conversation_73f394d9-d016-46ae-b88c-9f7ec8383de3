import 'package:sqflite/sqflite.dart';
import '../../../../core/database/database_service.dart';
import '../models/customer_account_model.dart';

class CustomerAccountDatabaseService {
  final DatabaseService _databaseService;

  CustomerAccountDatabaseService(this._databaseService);

  /// Add a new customer account entry
  Future<int> addCustomerAccountEntry(CustomerAccountModel entry) async {
    final db = await _databaseService.database;
    final entryMap = entry.toMap();
    entryMap.remove('id'); // Remove id for auto-increment

    print(
      '🔍 DEBUG: محاولة إدراج قيد حساب العميل في قاعدة البيانات: $entryMap',
    );
    try {
      final id = await db.insert(
        'customer_accounts',
        entryMap,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      print(
        '✅ DEBUG: تم إدراج قيد حساب العميل بنجاح في قاعدة البيانات بمعرف: $id',
      );
      return id;
    } catch (e) {
      print('❌ ERROR: فشل في إدراج قيد حساب العميل في قاعدة البيانات: $e');
      rethrow; // أعد إلقاء الخطأ ليتم التقاطه في الطبقات العليا
    }
  }

  /// Get customer account statement
  Future<List<CustomerAccountModel>> getCustomerAccountStatement(
    int customerId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final db = await _databaseService.database;

    String whereClause = 'customerId = ?';
    List<dynamic> whereArgs = [customerId];

    // أزل مؤقتاً هذا الجزء لغرض التصحيح
    /*
    if (fromDate != null) {
      whereClause += ' AND transactionDate >= ?';
      whereArgs.add(fromDate.toIso8601String());
    }

    if (toDate != null) {
      whereClause += ' AND transactionDate <= ?';
      whereArgs.add(toDate.toIso8601String());
    }
    */

    print(
      '🔍 DEBUG: CustomerAccountDatabaseService querying for customerId: $customerId',
    );
    print('🔍 DEBUG: WHERE clause: $whereClause, args: $whereArgs');

    try {
      final List<Map<String, dynamic>> maps = await db.query(
        'customer_accounts',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'transactionDate ASC, id ASC',
      );

      print(
        '📊 DEBUG: CustomerAccountDatabaseService fetched ${maps.length} raw maps for customerId: $customerId',
      );

      for (var map in maps) {
        print('📋 DEBUG: Customer account raw map: $map');
      }

      return List.generate(maps.length, (i) {
        return CustomerAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      print(
        '❌ ERROR: CustomerAccountDatabaseService failed to get statement: $e',
      );
      rethrow;
    }
  }

  /// Get customer balance
  Future<double> getCustomerBalance(int customerId) async {
    try {
      final db = await _databaseService.database;

      // Calculate balance: debits - credits
      final result = await db.rawQuery(
        '''
        SELECT
          SUM(CASE WHEN type IN ('sale_invoice', 'retail_debt') THEN amount ELSE 0 END) as totalDebits,
          SUM(CASE WHEN type IN ('payment', 'return', 'payment_in') THEN amount ELSE 0 END) as totalCredits
        FROM customer_accounts
        WHERE customerId = ?
      ''',
        [customerId],
      );

      if (result.isNotEmpty) {
        final totalDebits =
            (result.first['totalDebits'] as num?)?.toDouble() ?? 0.0;
        final totalCredits =
            (result.first['totalCredits'] as num?)?.toDouble() ?? 0.0;
        return totalDebits - totalCredits;
      }

      return 0.0;
    } catch (e) {
      throw Exception('Failed to get customer balance: $e');
    }
  }

  /// Get unpaid entries for customer
  Future<List<CustomerAccountModel>> getUnpaidEntries(int customerId) async {
    try {
      final db = await _databaseService.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customer_accounts',
        where: 'customerId = ? AND isPaid = 0 AND type IN (?, ?)',
        whereArgs: [customerId, 'sale_invoice', 'retail_debt'],
        orderBy: 'transactionDate ASC, id ASC',
      );

      return List.generate(maps.length, (i) {
        return CustomerAccountModel.fromMap(maps[i]);
      });
    } catch (e) {
      throw Exception('Failed to get unpaid entries: $e');
    }
  }

  /// Mark entry as paid
  Future<void> markEntryAsPaid(int entryId) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'customer_accounts',
        {'isPaid': 1},
        where: 'id = ?',
        whereArgs: [entryId],
      );
    } catch (e) {
      throw Exception('Failed to mark entry as paid: $e');
    }
  }

  /// Update customer account entry
  Future<void> updateCustomerAccountEntry(CustomerAccountModel entry) async {
    try {
      final db = await _databaseService.database;
      await db.update(
        'customer_accounts',
        entry.toMap(),
        where: 'id = ?',
        whereArgs: [entry.id],
      );
    } catch (e) {
      throw Exception('Failed to update customer account entry: $e');
    }
  }

  /// Delete customer account entry
  Future<void> deleteCustomerAccountEntry(int entryId) async {
    try {
      final db = await _databaseService.database;
      await db.delete(
        'customer_accounts',
        where: 'id = ?',
        whereArgs: [entryId],
      );
    } catch (e) {
      throw Exception('Failed to delete customer account entry: $e');
    }
  }

  /// Get total payments received for customer
  Future<double> getTotalPaymentsReceivedForCustomer(int customerId) async {
    try {
      final db = await _databaseService.database;
      final result = await db.rawQuery(
        // نجمع فقط الدفعات والمرتجعات (التي تقلل الرصيد المستحق على العميل)
        'SELECT SUM(amount) as total FROM customer_accounts WHERE customerId = ? AND type IN (?, ?)',
        [customerId, 'payment', 'return'],
      );
      return (result.first['total'] as num?)?.toDouble() ?? 0.0;
    } catch (e) {
      throw Exception('Failed to get total payments for customer: $e');
    }
  }

  /// Get customer account statistics
  Future<Map<String, dynamic>> getCustomerAccountStatistics(
    int customerId,
  ) async {
    try {
      final db = await _databaseService.database;

      // Get total entries count
      final countResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM customer_accounts WHERE customerId = ?',
        [customerId],
      );
      final totalEntries = countResult.first['count'] as int;

      // Get amounts by type
      final amountResult = await db.rawQuery(
        '''
        SELECT
          SUM(CASE WHEN type IN ('sale_invoice', 'retail_debt') THEN amount ELSE 0 END) as totalDebits,
          SUM(CASE WHEN type IN ('payment', 'return', 'payment_in') THEN amount ELSE 0 END) as totalCredits,
          SUM(CASE WHEN isPaid = 0 AND type IN ('sale_invoice', 'retail_debt') THEN amount ELSE 0 END) as unpaidAmount
        FROM customer_accounts
        WHERE customerId = ?
      ''',
        [customerId],
      );

      final totalDebits =
          (amountResult.first['totalDebits'] as num?)?.toDouble() ?? 0.0;
      final totalCredits =
          (amountResult.first['totalCredits'] as num?)?.toDouble() ?? 0.0;
      final unpaidAmount =
          (amountResult.first['unpaidAmount'] as num?)?.toDouble() ?? 0.0;
      final balance = totalDebits - totalCredits;

      // Get counts by type
      final typeResult = await db.rawQuery(
        '''
        SELECT type, COUNT(*) as count
        FROM customer_accounts
        WHERE customerId = ?
        GROUP BY type
      ''',
        [customerId],
      );

      final typeCounts = <String, int>{};
      for (final row in typeResult) {
        typeCounts[row['type'] as String] = row['count'] as int;
      }

      return {
        'totalEntries': totalEntries,
        'totalDebits': totalDebits,
        'totalCredits': totalCredits,
        'balance': balance,
        'unpaidAmount': unpaidAmount,
        'typeCounts': typeCounts,
      };
    } catch (e) {
      throw Exception('Failed to get customer account statistics: $e');
    }
  }
}
