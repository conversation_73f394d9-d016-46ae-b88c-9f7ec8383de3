import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import '../../features/sales/domain/entities/sale.dart';
import '../../features/sales/domain/entities/sale_item.dart';
import '../../features/purchases/domain/entities/purchase.dart';
import '../../features/purchases/domain/entities/purchase_item.dart';
import '../../features/customers/domain/entities/customer.dart';
import '../../features/suppliers/domain/entities/supplier.dart';

/// خدمة تصدير PDF احترافية للفواتير والتقارير
class PDFService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0.00');

  // إضافة تحميل الخطوط كخصائص ثابتة للكلاس
  static late pw.Font _arabicRegularFont;
  static late pw.Font _arabicBoldFont;
  static bool _fontsLoaded = false;

  static Future<void> loadFonts() async {
    if (_fontsLoaded) return; // لضمان التحميل مرة واحدة فقط
    try {
      // تحميل خط Cairo الذي يدعم العربية والإنجليزية والرموز
      final ByteData fontData = await rootBundle.load(
        'assets/fonts/cairo/Cairo-Regular.ttf',
      );
      _arabicRegularFont = pw.Font.ttf(fontData);

      final ByteData fontDataBold = await rootBundle.load(
        'assets/fonts/cairo/Cairo-Bold.ttf',
      );
      _arabicBoldFont = pw.Font.ttf(fontDataBold);
      _fontsLoaded = true;
      debugPrint('✅ تم تحميل خطوط Cairo لـ PDF بنجاح.');
    } catch (e) {
      debugPrint(
        '❌ فشل تحميل خطوط Cairo لـ PDF: $e. محاولة استخدام Noto Sans Arabic...',
      );
      // محاولة احتياطية باستخدام Noto Sans Arabic
      try {
        final ByteData fontData = await rootBundle.load(
          'assets/fonts/noto_arabic/NotoSansArabic-Regular.ttf',
        );
        _arabicRegularFont = pw.Font.ttf(fontData);

        final ByteData fontDataBold = await rootBundle.load(
          'assets/fonts/noto_arabic/NotoSansArabic-Bold.ttf',
        );
        _arabicBoldFont = pw.Font.ttf(fontDataBold);
        _fontsLoaded = true;
        debugPrint('✅ تم تحميل خطوط Noto Sans Arabic احتياطياً لـ PDF.');
      } catch (e2) {
        debugPrint(
          '❌ فشل تحميل جميع الخطوط لـ PDF: $e2. سيتم استخدام الخطوط الافتراضية.',
        );
        _fontsLoaded = false;
      }
    }
  }

  /// تصدير فاتورة مبيعات إلى PDF
  static Future<Uint8List> generateSaleInvoicePDF({
    required Sale sale,
    required List<SaleItem> items,
    Customer? customer,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
    String? companyType = 'مؤسسة تجارية',
    List<String>? companyPhones,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط العربية المحملة مسبقاً
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: _fontsLoaded
            ? pw.ThemeData.withFont(
                base: _arabicRegularFont,
                bold: _arabicBoldFont,
              )
            : pw.ThemeData.base(),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة الجديد
              _buildInvoiceHeaderV2(
                companyName: companyName!,
                companyType:
                    companyType ?? 'مؤسسة تجارية', // تأكد من تمرير companyType
                companyAddress: companyAddress!,
                companyPhone: companyPhone!,
                companyPhones:
                    companyPhones ??
                    [companyPhone!], // تأكد من تمرير companyPhones
                invoiceTitle: 'فاتورة مبيعات',
                invoiceNumber: sale.id?.toString() ?? '---',
                invoiceDate: sale.saleDate,
                customer: customer, // تمرير العميل
                supplier: null, // لا يوجد مورد في المبيعات
                paymentType: 'نقدي', // مؤقتاً حتى نضيف الحقل للكيان
              ),

              pw.SizedBox(height: 20),

              // جدول العناصر الجديد
              _buildSaleItemsTableV2(items),

              pw.SizedBox(height: 20),

              // الإجماليات الجديدة
              _buildSaleTotalsV2(sale),

              pw.Spacer(),

              // تذييل الفاتورة الجديد
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// تصدير فاتورة مشتريات إلى PDF
  static Future<Uint8List> generatePurchaseInvoicePDF({
    required Purchase purchase,
    required List<PurchaseItem> items,
    Supplier? supplier,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
    String? companyType = 'مؤسسة تجارية',
    List<String>? companyPhones,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط العربية المحملة مسبقاً
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: _fontsLoaded
            ? pw.ThemeData.withFont(
                base: _arabicRegularFont,
                bold: _arabicBoldFont,
              )
            : pw.ThemeData.base(),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة الجديد
              _buildInvoiceHeaderV2(
                companyName: companyName!,
                companyType: companyType ?? 'مؤسسة تجارية',
                companyAddress: companyAddress!,
                companyPhone: companyPhone!,
                companyPhones: companyPhones ?? [companyPhone!],
                invoiceTitle: 'فاتورة مشتريات',
                invoiceNumber: purchase.id?.toString() ?? '---',
                invoiceDate: purchase.purchaseDate,
                customer: null, // لا يوجد عميل لفاتورة الشراء
                supplier: supplier, // تمرير المورد
                paymentType: 'نقدي', // مؤقتاً حتى نضيف الحقل للكيان
              ),

              pw.SizedBox(height: 20),

              // جدول العناصر الجديد
              _buildPurchaseItemsTableV2(items),

              pw.SizedBox(height: 20),

              // الإجماليات الجديدة
              _buildPurchaseTotalsV2(purchase),

              pw.Spacer(),

              // تذييل الفاتورة الجديد
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// طباعة PDF
  static Future<void> printPDF(Uint8List pdfData, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: title,
    );
  }

  /// حفظ PDF في الجهاز
  static Future<String> savePDF(Uint8List pdfData, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName.pdf');
    await file.writeAsBytes(pdfData);
    return file.path;
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfData, String fileName) async {
    await Printing.sharePdf(bytes: pdfData, filename: '$fileName.pdf');
  }

  // ===== الدوال الجديدة للتصميم المحدث =====

  /// بناء رأس الفاتورة الجديد (النسخة الثانية)
  static pw.Widget _buildInvoiceHeaderV2({
    required String companyName, // أسامة ماركت
    required String companyType, // للمواد الغذائية
    required String companyAddress, // محطة المسعودي - الشارع العام
    required String companyPhone, // للتوافق مع الكود القديم
    required List<String> companyPhones, // ********* - *********
    required String invoiceTitle, // فاتورة مبيعات
    required String invoiceNumber, // رقم الفاتورة
    required DateTime invoiceDate, // التاريخ
    Customer? customer, // اسم العميل (للمبيعات)
    Supplier? supplier, // اسم المورد (للمشتريات)
    required String paymentType, // نقدي / آجل / مدفوعة جزئياً
  }) {
    // تحديد اسم الكيان
    final String entityName = customer?.name ?? supplier?.name ?? 'غير محدد';
    final String entityTypeLabel = customer != null
        ? 'اسم العميل'
        : 'اسم المورد';
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch, // للتمدد في العرض
      children: [
        // الصف الأول: معلومات المتجر (يمين عربي / يسار إنجليزي)
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            // القسم الإنجليزي (يسار)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start, // محاذاة لليسار
              children: [
                pw.Text(
                  'Osama Market',
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: _fontsLoaded ? _arabicBoldFont : null,
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.Text(
                  'For Food Supplies',
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.Text(
                  'Al-Masoudi Station - Main Street',
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.Text(
                  companyPhones.join(' - '), // إزالة رمز الهاتف مؤقتاً
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
              ],
            ),

            // القسم العربي (يمين)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end, // محاذاة لليمين
              children: [
                pw.Text(
                  companyName,
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: _fontsLoaded ? _arabicBoldFont : null,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyType,
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyAddress,
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyPhones.join(' - '), // إزالة رمز الهاتف مؤقتاً
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _fontsLoaded ? _arabicRegularFont : null,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ],
        ),
        pw.SizedBox(height: 15),
        pw.Divider(height: 1, color: PdfColors.grey), // خط فاصل
        pw.SizedBox(height: 15),

        // الصف الثاني: عنوان الفاتورة وتصنيف الدفع
        pw.Column(
          children: [
            pw.Text(
              invoiceTitle, // فاتورة مبيعات / مشتريات
              style: pw.TextStyle(
                fontSize: 20,
                fontWeight: pw.FontWeight.bold,
                font: _fontsLoaded ? _arabicBoldFont : null,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.Text(
              paymentType, // نقدي / آجل / مدفوعة جزئياً
              style: pw.TextStyle(
                fontSize: 14,
                font: _fontsLoaded ? _arabicRegularFont : null,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
        pw.SizedBox(height: 15),

        // الصف الثالث: اسم العميل/المورد والتاريخ ورقم الفاتورة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            // التاريخ ورقم الفاتورة (يسار)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start, // محاذاة لليسار
              children: [
                pw.RichText(
                  text: pw.TextSpan(
                    children: [
                      pw.TextSpan(
                        text: 'التاريخ: ',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 12,
                          font: _fontsLoaded ? _arabicBoldFont : null,
                        ),
                      ),
                      pw.TextSpan(
                        text: _dateFormat.format(invoiceDate),
                        style: pw.TextStyle(
                          fontSize: 12,
                          font: _fontsLoaded ? _arabicRegularFont : null,
                        ),
                      ),
                    ],
                  ),
                  textDirection:
                      pw.TextDirection.rtl, // اتجاه لليمين للنص العربي
                ),
                pw.RichText(
                  text: pw.TextSpan(
                    children: [
                      pw.TextSpan(
                        text: 'رقم الفاتورة: ',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 12,
                          font: _fontsLoaded ? _arabicBoldFont : null,
                        ),
                      ),
                      pw.TextSpan(
                        text: invoiceNumber,
                        style: pw.TextStyle(
                          fontSize: 12,
                          font: _fontsLoaded ? _arabicRegularFont : null,
                        ),
                      ),
                    ],
                  ),
                  textDirection:
                      pw.TextDirection.rtl, // اتجاه لليمين للنص العربي
                ),
              ],
            ),

            // اسم العميل/المورد (يمين)
            pw.RichText(
              text: pw.TextSpan(
                children: [
                  pw.TextSpan(
                    text: '$entityTypeLabel: ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 12,
                      font: _fontsLoaded ? _arabicBoldFont : null,
                    ),
                  ),
                  pw.TextSpan(
                    text: entityName,
                    style: pw.TextStyle(
                      fontSize: 12,
                      font: _fontsLoaded ? _arabicRegularFont : null,
                    ),
                  ),
                ],
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جدول العناصر الجديد للمبيعات - يطابق النموذج المرفق
  static pw.Widget _buildSaleItemsTableV2(List<SaleItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.2), // القيمة
        1: const pw.FlexColumnWidth(1.2), // السعر
        2: const pw.FlexColumnWidth(1), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.5), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2('القيمة', isHeader: true),
            _buildTableCellV2('السعر', isHeader: true),
            _buildTableCellV2('الكمية', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('م', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null ? ' ${item.unit}' : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(_currencyFormat.format(item.totalPrice)),
              _buildTableCellV2(_currencyFormat.format(item.unitPrice)),
              _buildTableCellV2('${item.quantity ?? 1}$unitText'),
              _buildTableCellV2(
                item.description ?? 'Product ${item.productId}',
              ),
              _buildTableCellV2(index.toString()),
            ],
          );
        }),

        // صفوف فارغة لملء المساحة أو ضمان الحد الأدنى (ترتيب RTL)
        ...List.generate(
          (6 - items.length).clamp(0, 6), // لضمان 6 صفوف على الأقل
          (index) => pw.TableRow(
            children: [
              _buildTableCellV2(''), // القيمة
              _buildTableCellV2(''), // السعر
              _buildTableCellV2(''), // الكمية
              _buildTableCellV2(''), // البيان
              _buildTableCellV2(''), // م
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خلية الجدول الجديدة
  static pw.Widget _buildTableCellV2(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          font: _fontsLoaded
              ? (isHeader ? _arabicBoldFont : _arabicRegularFont)
              : null,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// بناء إجماليات المبيعات الجديدة - يطابق النموذج المرفق
  static pw.Widget _buildSaleTotalsV2(Sale sale) {
    return pw.Column(
      children: [
        pw.SizedBox(height: 16),

        // الإجمالي محاذاة لليمين
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.symmetric(vertical: 12),
          child: pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Text(
              'الإجمالي: ${_currencyFormat.format(sale.totalAmount - sale.discountAmount)}',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                font: _fontsLoaded ? _arabicBoldFont : null,
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ),

        pw.SizedBox(height: 20),

        // قسم الملاحظات
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey400),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'ملاحظات:',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  font: _fontsLoaded ? _arabicBoldFont : null,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                '• التأكد من استلام جميع بنود الفاتورة',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _fontsLoaded ? _arabicRegularFont : null,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'شكراً لتعاملكم معنا',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _fontsLoaded ? _arabicRegularFont : null,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف الإجمالي الجديد
  static pw.Widget _buildTotalRowV2(
    String label,
    double amount, {
    bool isFinal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة الجديد
  static pw.Widget _buildInvoiceFooterV2() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Align(
        alignment: pw.Alignment.centerRight,
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            font: _fontsLoaded ? _arabicBoldFont : null,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ),
    );
  }

  /// بناء جدول العناصر الجديد للمشتريات
  static pw.Widget _buildPurchaseItemsTableV2(List<PurchaseItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.8), // القيمة
        1: const pw.FlexColumnWidth(1.5), // السعر
        2: const pw.FlexColumnWidth(1.2), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.8), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2('القيمة', isHeader: true),
            _buildTableCellV2('السعر', isHeader: true),
            _buildTableCellV2('الكمية', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('م', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null ? ' ${item.unit}' : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(_currencyFormat.format(item.totalPrice)),
              _buildTableCellV2(_currencyFormat.format(item.unitPrice)),
              _buildTableCellV2('${item.quantity}$unitText'),
              _buildTableCellV2('منتج ${item.productId}'), // يمكن تحسينه لاحقاً
              _buildTableCellV2(index.toString()),
            ],
          );
        }),

        // صفوف فارغة لملء المساحة أو ضمان الحد الأدنى (ترتيب RTL)
        ...List.generate(
          (6 - items.length).clamp(0, 6), // لضمان 6 صفوف على الأقل
          (index) => pw.TableRow(
            children: [
              _buildTableCellV2(''), // القيمة
              _buildTableCellV2(''), // السعر
              _buildTableCellV2(''), // الكمية
              _buildTableCellV2(''), // البيان
              _buildTableCellV2(''), // م
            ],
          ),
        ),
      ],
    );
  }

  /// بناء إجماليات المشتريات الجديدة
  static pw.Widget _buildPurchaseTotalsV2(Purchase purchase) {
    return pw.Row(
      children: [
        // الملاحظات (يمين)
        pw.Expanded(
          flex: 2,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end, // محاذاة لليمين
              children: [
                pw.Text(
                  'ملاحظات:',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Text(''),
              ],
            ),
          ),
        ),

        pw.SizedBox(width: 8),

        // الإجماليات (يسار)
        pw.Expanded(
          flex: 1,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              children: [
                _buildTotalRowV2('الإجمالي:', purchase.totalAmount),
                if (purchase.discountAmount > 0)
                  _buildTotalRowV2('الخصم:', purchase.discountAmount),
                _buildTotalRowV2(
                  'الصافي:',
                  purchase.totalAmount - purchase.discountAmount,
                  isFinal: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
