import 'dart:io';
import 'dart:typed_data';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import '../../features/sales/domain/entities/sale.dart';
import '../../features/sales/domain/entities/sale_item.dart';
import '../../features/purchases/domain/entities/purchase.dart';
import '../../features/purchases/domain/entities/purchase_item.dart';
import '../../features/customers/domain/entities/customer.dart';
import '../../features/suppliers/domain/entities/supplier.dart';

/// خدمة تصدير PDF احترافية للفواتير والتقارير
class PDFService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0.00');

  /// تصدير فاتورة مبيعات إلى PDF
  static Future<Uint8List> generateSaleInvoicePDF({
    required Sale sale,
    required List<SaleItem> items,
    Customer? customer,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
    String? companyType = 'مؤسسة تجارية',
    List<String>? companyPhones,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط الافتراضية للنظام
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة الجديد
              _buildInvoiceHeaderV2(
                companyName: companyName!,
                companyAddress: companyAddress!,
                companyPhone: companyPhone!,
                companyType: companyType,
                companyPhones: companyPhones ?? [companyPhone!],
                invoiceTitle: 'فاتورة مبيعات',
                invoiceNumber: sale.id?.toString() ?? '---',
                invoiceDate: sale.saleDate,
                customer: customer,
                paymentType: sale.paymentType,
              ),

              pw.SizedBox(height: 20),

              // جدول العناصر الجديد
              _buildSaleItemsTableV2(items),

              pw.SizedBox(height: 20),

              // الإجماليات الجديدة
              _buildSaleTotalsV2(sale),

              pw.Spacer(),

              // تذييل الفاتورة الجديد
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// تصدير فاتورة مشتريات إلى PDF
  static Future<Uint8List> generatePurchaseInvoicePDF({
    required Purchase purchase,
    required List<PurchaseItem> items,
    Supplier? supplier,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
  }) async {
    final pdf = pw.Document();

    // تحميل خط عربي
    final arabicFont = await PdfGoogleFonts.notoSansArabicRegular();
    final arabicFontBold = await PdfGoogleFonts.notoSansArabicBold();

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: pw.ThemeData.withFont(base: arabicFont, bold: arabicFontBold),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة
              _buildInvoiceHeader(
                companyName: companyName!,
                companyAddress: companyAddress!,
                companyPhone: companyPhone!,
                invoiceTitle: 'فاتورة مشتريات',
                invoiceNumber: purchase.id?.toString() ?? '---',
                invoiceDate: purchase.purchaseDate,
              ),

              pw.SizedBox(height: 20),

              // معلومات المورد
              _buildSupplierInfo(supplier),

              pw.SizedBox(height: 20),

              // جدول العناصر
              _buildPurchaseItemsTable(items),

              pw.SizedBox(height: 20),

              // الإجماليات
              _buildPurchaseTotals(purchase),

              pw.Spacer(),

              // تذييل الفاتورة
              _buildInvoiceFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء رأس الفاتورة
  static pw.Widget _buildInvoiceHeader({
    required String companyName,
    required String companyAddress,
    required String companyPhone,
    required String invoiceTitle,
    required String invoiceNumber,
    required DateTime invoiceDate,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // معلومات الشركة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                companyName,
                style: pw.TextStyle(
                  fontSize: 20,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text(companyAddress, style: const pw.TextStyle(fontSize: 12)),
              pw.Text(companyPhone, style: const pw.TextStyle(fontSize: 12)),
            ],
          ),

          // معلومات الفاتورة
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                invoiceTitle,
                style: pw.TextStyle(
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.green800,
                ),
              ),
              pw.SizedBox(height: 5),
              pw.Text('رقم الفاتورة: $invoiceNumber'),
              pw.Text('التاريخ: ${_dateFormat.format(invoiceDate)}'),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء معلومات العميل
  static pw.Widget _buildCustomerInfo(Customer? customer) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'بيانات العميل',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          pw.Text('الاسم: ${customer?.name ?? 'عميل عابر'}'),
          if (customer?.phone != null) pw.Text('الهاتف: ${customer!.phone}'),
          if (customer?.address != null)
            pw.Text('العنوان: ${customer!.address}'),
        ],
      ),
    );
  }

  /// بناء معلومات المورد
  static pw.Widget _buildSupplierInfo(Supplier? supplier) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'بيانات المورد',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 8),
          pw.Text('الاسم: ${supplier?.name ?? 'غير محدد'}'),
          if (supplier?.phone != null) pw.Text('الهاتف: ${supplier!.phone}'),
          if (supplier?.address != null)
            pw.Text('العنوان: ${supplier!.address}'),
        ],
      ),
    );
  }

  /// بناء جدول عناصر المبيعات
  static pw.Widget _buildSaleItemsTable(List<SaleItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(3),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
        4: const pw.FlexColumnWidth(2),
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('م', isHeader: true),
            _buildTableCell('الوصف', isHeader: true),
            _buildTableCell('الكمية', isHeader: true),
            _buildTableCell('السعر', isHeader: true),
            _buildTableCell('الإجمالي', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;

          return pw.TableRow(
            children: [
              _buildTableCell(index.toString()),
              _buildTableCell(item.description ?? 'منتج'),
              _buildTableCell(item.quantity?.toString() ?? '1'),
              _buildTableCell('${_currencyFormat.format(item.unitPrice)} ر.ي'),
              _buildTableCell('${_currencyFormat.format(item.totalPrice)} ر.ي'),
            ],
          );
        }),
      ],
    );
  }

  /// بناء جدول عناصر المشتريات
  static pw.Widget _buildPurchaseItemsTable(List<PurchaseItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(3),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
        4: const pw.FlexColumnWidth(2),
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('م', isHeader: true),
            _buildTableCell('المنتج', isHeader: true),
            _buildTableCell('الكمية', isHeader: true),
            _buildTableCell('السعر', isHeader: true),
            _buildTableCell('الإجمالي', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;

          return pw.TableRow(
            children: [
              _buildTableCell(index.toString()),
              _buildTableCell('منتج ${item.productId}'), // يمكن تحسينه لاحقاً
              _buildTableCell(item.quantity.toString()),
              _buildTableCell('${_currencyFormat.format(item.unitPrice)} ر.ي'),
              _buildTableCell('${_currencyFormat.format(item.totalPrice)} ر.ي'),
            ],
          );
        }),
      ],
    );
  }

  /// بناء خلية الجدول
  static pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء إجماليات المبيعات
  static pw.Widget _buildSaleTotals(Sale sale) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          _buildTotalRow(
            'المجموع الفرعي:',
            sale.totalAmount - sale.discountAmount,
          ),
          if (sale.discountAmount > 0)
            _buildTotalRow('الخصم:', sale.discountAmount, isDiscount: true),
          _buildTotalRow('الإجمالي:', sale.totalAmount, isFinal: true),
          _buildTotalRow('المدفوع:', sale.totalPaidAmount),
          if (sale.dueAmount > 0)
            _buildTotalRow('المتبقي:', sale.dueAmount, isDue: true),
        ],
      ),
    );
  }

  /// بناء إجماليات المشتريات
  static pw.Widget _buildPurchaseTotals(Purchase purchase) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        children: [
          _buildTotalRow(
            'المجموع الفرعي:',
            purchase.totalAmount - purchase.discountAmount,
          ),
          if (purchase.discountAmount > 0)
            _buildTotalRow('الخصم:', purchase.discountAmount, isDiscount: true),
          _buildTotalRow('الإجمالي:', purchase.totalAmount, isFinal: true),
          _buildTotalRow('المدفوع:', purchase.totalPaidAmount),
          if (purchase.dueAmount > 0)
            _buildTotalRow('المتبقي:', purchase.dueAmount, isDue: true),
        ],
      ),
    );
  }

  /// بناء صف الإجمالي
  static pw.Widget _buildTotalRow(
    String label,
    double amount, {
    bool isFinal = false,
    bool isDiscount = false,
    bool isDue = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isFinal ? 14 : 12,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            '${_currencyFormat.format(amount)} ر.ي',
            style: pw.TextStyle(
              fontSize: isFinal ? 14 : 12,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: isDiscount
                  ? PdfColors.red
                  : isDue
                  ? PdfColors.orange
                  : isFinal
                  ? PdfColors.green800
                  : PdfColors.black,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة
  static pw.Widget _buildInvoiceFooter() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Center(
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(
            fontSize: 14,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue800,
          ),
        ),
      ),
    );
  }

  /// طباعة PDF
  static Future<void> printPDF(Uint8List pdfData, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: title,
    );
  }

  /// حفظ PDF في الجهاز
  static Future<String> savePDF(Uint8List pdfData, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName.pdf');
    await file.writeAsBytes(pdfData);
    return file.path;
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfData, String fileName) async {
    await Printing.sharePdf(bytes: pdfData, filename: '$fileName.pdf');
  }

  // ===== الدوال الجديدة للتصميم المحدث =====

  /// بناء رأس الفاتورة الجديد (النسخة الثانية)
  static pw.Widget _buildInvoiceHeaderV2({
    required String companyName,
    required String companyAddress,
    required String companyPhone,
    required String companyType,
    required List<String> companyPhones,
    required String invoiceTitle,
    required String invoiceNumber,
    required DateTime invoiceDate,
    Customer? customer,
    String? paymentType,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey400),
      ),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          // معلومات الشركة (يمين)
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  companyName,
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 4),
                pw.Text(companyType, style: const pw.TextStyle(fontSize: 12)),
                pw.Text(
                  companyAddress,
                  style: const pw.TextStyle(fontSize: 12),
                ),
                ...companyPhones.map(
                  (phone) => pw.Text(
                    'تلفون: $phone',
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
          ),

          // معلومات الفاتورة والعميل (يسار)
          pw.Expanded(
            flex: 2,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  invoiceTitle,
                  style: pw.TextStyle(
                    fontSize: 16,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
                pw.SizedBox(height: 8),
                pw.Text('رقم: $invoiceNumber'),
                pw.Text('التاريخ: ${_dateFormat.format(invoiceDate)}'),
                if (customer != null) ...[
                  pw.SizedBox(height: 8),
                  pw.Text('العميل: ${customer.name}'),
                  if (customer.phone != null)
                    pw.Text('تلفون: ${customer.phone}'),
                ],
                if (paymentType != null) ...[
                  pw.SizedBox(height: 8),
                  pw.Text('طريقة الدفع: $paymentType'),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء جدول العناصر الجديد للمبيعات
  static pw.Widget _buildSaleItemsTableV2(List<SaleItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(0.8), // م
        1: const pw.FlexColumnWidth(3), // البيان
        2: const pw.FlexColumnWidth(1.2), // الكمية
        3: const pw.FlexColumnWidth(1.5), // السعر
        4: const pw.FlexColumnWidth(1.8), // القيمة
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2('م', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('الكمية', isHeader: true),
            _buildTableCellV2('السعر', isHeader: true),
            _buildTableCellV2('القيمة', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null ? ' ${item.unit}' : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(index.toString()),
              _buildTableCellV2(item.description ?? 'منتج'),
              _buildTableCellV2('${item.quantity ?? 1}$unitText'),
              _buildTableCellV2(_currencyFormat.format(item.unitPrice)),
              _buildTableCellV2(_currencyFormat.format(item.totalPrice)),
            ],
          );
        }),

        // صفوف فارغة للتصميم
        ...List.generate(
          3,
          (index) => pw.TableRow(
            children: [
              _buildTableCellV2(''),
              _buildTableCellV2(''),
              _buildTableCellV2(''),
              _buildTableCellV2(''),
              _buildTableCellV2(''),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء خلية الجدول الجديدة
  static pw.Widget _buildTableCellV2(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء إجماليات المبيعات الجديدة
  static pw.Widget _buildSaleTotalsV2(Sale sale) {
    return pw.Row(
      children: [
        // الملاحظات (يمين)
        pw.Expanded(
          flex: 2,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'ملاحظات:',
                  style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 20),
                pw.Text(''),
              ],
            ),
          ),
        ),

        pw.SizedBox(width: 8),

        // الإجماليات (يسار)
        pw.Expanded(
          flex: 1,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              children: [
                _buildTotalRowV2('الإجمالي:', sale.totalAmount),
                if (sale.discountAmount > 0)
                  _buildTotalRowV2('الخصم:', sale.discountAmount),
                _buildTotalRowV2(
                  'الصافي:',
                  sale.totalAmount - sale.discountAmount,
                  isFinal: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// بناء صف الإجمالي الجديد
  static pw.Widget _buildTotalRowV2(
    String label,
    double amount, {
    bool isFinal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة الجديد
  static pw.Widget _buildInvoiceFooterV2() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Center(
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
        ),
      ),
    );
  }
}
