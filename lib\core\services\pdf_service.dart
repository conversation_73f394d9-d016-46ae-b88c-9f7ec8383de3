import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import '../../features/sales/domain/entities/sale.dart';
import '../../features/sales/domain/entities/sale_item.dart';
import '../../features/purchases/domain/entities/purchase.dart';
import '../../features/purchases/domain/entities/purchase_item.dart';
import '../../features/customers/domain/entities/customer.dart';
import '../../features/suppliers/domain/entities/supplier.dart';

/// خدمة تصدير PDF احترافية للفواتير والتقارير
class PDFService {
  static final DateFormat _dateFormat = DateFormat('dd/MM/yyyy');
  static final NumberFormat _currencyFormat = NumberFormat('#,##0.00');

  // إضافة تحميل الخطوط كخصائص ثابتة للكلاس
  static late pw.Font _cairoRegularFont;
  static late pw.Font _cairoBoldFont;
  static late pw.Font _notoRegularFont;
  static late pw.Font _notoBoldFont;
  static bool _fontsLoaded = false;

  static Future<void> loadFonts() async {
    if (_fontsLoaded) return; // لضمان التحميل مرة واحدة فقط
    try {
      // تحميل خطوط Cairo (أساسي للعربية في الواجهة)
      final ByteData cairoRegular = await rootBundle.load(
        'assets/fonts/cairo/Cairo-Regular.ttf',
      );
      _cairoRegularFont = pw.Font.ttf(cairoRegular);
      final ByteData cairoBold = await rootBundle.load(
        'assets/fonts/cairo/Cairo-Bold.ttf',
      );
      _cairoBoldFont = pw.Font.ttf(cairoBold);

      // تحميل خط Noto Sans Arabic (أساسي للعربية في PDF)
      final ByteData notoRegular = await rootBundle.load(
        'assets/fonts/noto_arabic/NotoSansArabic-Regular.ttf',
      );
      _notoRegularFont = pw.Font.ttf(notoRegular);
      final ByteData notoBold = await rootBundle.load(
        'assets/fonts/noto_arabic/NotoSansArabic-Bold.ttf',
      );
      _notoBoldFont = pw.Font.ttf(notoBold);

      // تم إزالة الخطوط الإنجليزية لتبسيط النظام

      _fontsLoaded = true;
      debugPrint('✅ تم تحميل جميع الخطوط لـ PDF بنجاح: Cairo, Noto.');
    } catch (e) {
      debugPrint(
        '❌ فشل تحميل بعض الخطوط لـ PDF: $e. سيتم استخدام الخطوط الافتراضية للمكتبة.',
      );
      _fontsLoaded = false;
      // Fallback to library's default fonts if custom ones fail
      _cairoRegularFont = pw.Font.helvetica();
      _cairoBoldFont = pw.Font.helveticaBold();
      _notoRegularFont = pw.Font.helvetica();
      _notoBoldFont = pw.Font.helveticaBold();
    }
  }

  /// دالة مساعدة لاختيار الخط المناسب حسب نوع النص
  static pw.Font _getAppropriateFont(String text, {bool isBold = false}) {
    if (!_fontsLoaded) {
      return isBold ? pw.Font.helveticaBold() : pw.Font.helvetica();
    }

    // فحص إذا كان النص يحتوي على أحرف عربية
    final arabicRegex = RegExp(
      r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]',
    );
    final hasArabic = arabicRegex.hasMatch(text);

    if (hasArabic) {
      // استخدام الخط العربي للنصوص العربية
      return isBold ? _notoBoldFont : _notoRegularFont;
    } else {
      // استخدام الخط العربي للنصوص الإنجليزية أيضاً (Noto Sans Arabic يدعم اللاتينية)
      return isBold ? _notoBoldFont : _notoRegularFont;
    }
  }

  /// تصدير فاتورة مبيعات إلى PDF
  static Future<Uint8List> generateSaleInvoicePDF({
    required Sale sale,
    required List<SaleItem> items,
    Customer? customer,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
    String? companyType = 'مؤسسة تجارية',
    List<String>? companyPhones,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط العربية المحملة مسبقاً
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl, // اتجاه عام للصفحة
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded
              ? _notoRegularFont
              : pw.Font.helvetica(), // Noto هو الخط الأساسي للعربية
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [
            pw.Font.helvetica(), // Fallback نهائي للمكتبة
            pw.Font.helveticaBold(),
          ],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة الجديد
              _buildInvoiceHeaderV2(
                companyName: companyName!,
                companyType:
                    companyType ?? 'مؤسسة تجارية', // تأكد من تمرير companyType
                companyAddress: companyAddress!,
                companyPhones:
                    companyPhones ??
                    [companyPhone!], // تأكد من تمرير companyPhones
                invoiceTitle: 'فاتورة مبيعات',
                invoiceNumber: sale.id?.toString() ?? '---',
                invoiceDate: sale.saleDate,
                customer: customer, // تمرير العميل
                supplier: null, // لا يوجد مورد في المبيعات
                paymentType: 'نقدي', // مؤقتاً حتى نضيف الحقل للكيان
              ),

              pw.SizedBox(height: 20),

              // جدول العناصر الجديد
              _buildSaleItemsTableV2(items),

              pw.SizedBox(height: 20),

              // الإجماليات الجديدة
              _buildSaleTotalsV2(sale),

              pw.Spacer(),

              // تذييل الفاتورة الجديد
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// تصدير فاتورة مشتريات إلى PDF
  static Future<Uint8List> generatePurchaseInvoicePDF({
    required Purchase purchase,
    required List<PurchaseItem> items,
    Supplier? supplier,
    String? companyName = 'شركة السوق التجارية',
    String? companyAddress = 'المملكة العربية السعودية',
    String? companyPhone = '+966 XX XXX XXXX',
    String? companyType = 'مؤسسة تجارية',
    List<String>? companyPhones,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط العربية المحملة مسبقاً
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl, // اتجاه عام للصفحة
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // رأس الفاتورة الجديد
              _buildInvoiceHeaderV2(
                companyName: companyName!,
                companyType: companyType ?? 'مؤسسة تجارية',
                companyAddress: companyAddress!,
                companyPhones: companyPhones ?? [companyPhone!],
                invoiceTitle: 'فاتورة مشتريات',
                invoiceNumber: purchase.id?.toString() ?? '---',
                invoiceDate: purchase.purchaseDate,
                customer: null, // لا يوجد عميل لفاتورة الشراء
                supplier: supplier, // تمرير المورد
                paymentType: 'نقدي', // مؤقتاً حتى نضيف الحقل للكيان
              ),

              pw.SizedBox(height: 20),

              // جدول العناصر الجديد
              _buildPurchaseItemsTableV2(items),

              pw.SizedBox(height: 20),

              // الإجماليات الجديدة
              _buildPurchaseTotalsV2(purchase),

              pw.Spacer(),

              // تذييل الفاتورة الجديد
              _buildInvoiceFooterV2(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// طباعة PDF
  static Future<void> printPDF(Uint8List pdfData, String title) async {
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdfData,
      name: title,
    );
  }

  /// حفظ PDF في الجهاز
  static Future<String> savePDF(Uint8List pdfData, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName.pdf');
    await file.writeAsBytes(pdfData);
    return file.path;
  }

  /// مشاركة PDF
  static Future<void> sharePDF(Uint8List pdfData, String fileName) async {
    await Printing.sharePdf(bytes: pdfData, filename: '$fileName.pdf');
  }

  /// توليد PDF لكشف حساب العميل
  static Future<Uint8List> generateCustomerStatementPDF({
    required Customer customer,
    required List<dynamic> statementItems, // CustomerAccountStatementItem
    DateTime? fromDate,
    DateTime? toDate,
    double openingBalance = 0.0,
    String? companyName,
    String? companyAddress,
    String? companyPhone,
  }) async {
    final pdf = pw.Document();

    // استخدام الخطوط المحملة مسبقاً
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl, // اتجاه عام للصفحة
        theme: pw.ThemeData.withFont(
          base: _fontsLoaded ? _notoRegularFont : pw.Font.helvetica(),
          bold: _fontsLoaded ? _notoBoldFont : pw.Font.helveticaBold(),
          fontFallback: [pw.Font.helvetica(), pw.Font.helveticaBold()],
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.stretch,
            children: [
              // رأس كشف الحساب
              _buildStatementHeader(
                customer: customer,
                fromDate: fromDate,
                toDate: toDate,
                companyName: companyName ?? 'أسامة ماركت',
                companyAddress:
                    companyAddress ?? 'محطة المسعودي - الشارع العام',
                companyPhone: companyPhone ?? '*********',
              ),

              pw.SizedBox(height: 20),

              // جدول كشف الحساب
              _buildStatementTable(statementItems, openingBalance),

              pw.Spacer(),

              // تذييل كشف الحساب
              _buildStatementFooter(),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  /// بناء رأس كشف الحساب
  static pw.Widget _buildStatementHeader({
    required Customer customer,
    DateTime? fromDate,
    DateTime? toDate,
    required String companyName,
    required String companyAddress,
    required String companyPhone,
  }) {
    final String dateRange = fromDate != null && toDate != null
        ? 'من ${_dateFormat.format(fromDate)} إلى ${_dateFormat.format(toDate)}'
        : 'جميع الفترات';

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch,
      children: [
        // معلومات الشركة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end,
              children: [
                pw.Text(
                  companyName,
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyAddress,
                  style: const pw.TextStyle(fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  'هاتف: $companyPhone',
                  style: const pw.TextStyle(fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ],
            ),
          ],
        ),

        pw.SizedBox(height: 15),
        pw.Divider(height: 1, color: PdfColors.grey),
        pw.SizedBox(height: 15),

        // عنوان كشف الحساب
        pw.Center(
          child: pw.Text(
            'كشف حساب العميل',
            style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        pw.SizedBox(height: 15),

        // معلومات العميل والفترة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.RichText(
              text: pw.TextSpan(
                children: [
                  pw.TextSpan(
                    text: 'اسم العميل: ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  pw.TextSpan(
                    text: customer.name,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.RichText(
              text: pw.TextSpan(
                children: [
                  pw.TextSpan(
                    text: 'الفترة: ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  pw.TextSpan(
                    text: dateRange,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جدول كشف الحساب
  static pw.Widget _buildStatementTable(
    List<dynamic> items,
    double openingBalance,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey),
      columnWidths: const {
        0: pw.FlexColumnWidth(2), // التاريخ
        1: pw.FlexColumnWidth(3), // البيان
        2: pw.FlexColumnWidth(2), // مدين
        3: pw.FlexColumnWidth(2), // دائن
        4: pw.FlexColumnWidth(2), // الرصيد
      },
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCellV2('التاريخ', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('مدين', isHeader: true),
            _buildTableCellV2('دائن', isHeader: true),
            _buildTableCellV2('الرصيد', isHeader: true),
          ],
        ),

        // صف الرصيد الافتتاحي
        if (openingBalance != 0.0)
          pw.TableRow(
            children: [
              _buildTableCellV2(''),
              _buildTableCellV2('الرصيد الافتتاحي'),
              _buildTableCellV2(
                openingBalance > 0 ? openingBalance.toStringAsFixed(2) : '',
              ),
              _buildTableCellV2(
                openingBalance < 0 ? (-openingBalance).toStringAsFixed(2) : '',
              ),
              _buildTableCellV2(openingBalance.toStringAsFixed(2)),
            ],
          ),

        // صفوف البيانات
        ...items.map((item) {
          // نحتاج للوصول لخصائص العنصر بشكل ديناميكي
          final date = _dateFormat.format(item.transaction.transactionDate);
          final description = item.transaction.description ?? 'بدون وصف';
          final debit = item.debit > 0 ? item.debit.toStringAsFixed(2) : '';
          final credit = item.credit > 0 ? item.credit.toStringAsFixed(2) : '';
          final balance = item.runningBalance.toStringAsFixed(2);

          return pw.TableRow(
            children: [
              _buildTableCellV2(date),
              _buildTableCellV2(description),
              _buildTableCellV2(debit),
              _buildTableCellV2(credit),
              _buildTableCellV2(balance),
            ],
          );
        }).toList(),
      ],
    );
  }

  /// بناء تذييل كشف الحساب
  static pw.Widget _buildStatementFooter() {
    return pw.Align(
      alignment: pw.Alignment.centerRight,
      child: pw.Text(
        'تم إنشاء هذا الكشف بواسطة نظام أسامة ماركت',
        style: const pw.TextStyle(fontSize: 10),
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  // تم نقل دالة sharePDF إلى الأعلى لتجنب التكرار

  // ===== الدوال الجديدة للتصميم المحدث =====

  /// بناء رأس الفاتورة الجديد ثنائي اللغة (النسخة الثانية)
  static pw.Widget _buildInvoiceHeaderV2({
    required String companyName, // أسامة ماركت
    required String companyType, // للمواد الغذائية
    required String companyAddress, // محطة المسعودي - الشارع العام
    required List<String> companyPhones, // ********* - *********
    required String invoiceTitle, // فاتورة مبيعات
    required String invoiceNumber, // رقم الفاتورة
    required DateTime invoiceDate, // التاريخ
    Customer? customer, // اسم العميل (للمبيعات)
    Supplier? supplier, // اسم المورد (للمشتريات)
    required String paymentType, // نقدي / آجل / مدفوعة جزئياً
  }) {
    // تحديد اسم الكيان
    final String entityName = customer?.name ?? supplier?.name ?? 'غير محدد';
    final String entityTypeLabel = customer != null
        ? 'اسم العميل'
        : 'اسم المورد';
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.stretch, // للتمدد في العرض
      children: [
        // الصف الأول: معلومات المتجر (كتلة واحدة على اليمين، تحتوي على العربي والإنجليزي)
        pw.Row(
          mainAxisAlignment:
              pw.MainAxisAlignment.spaceBetween, // لدفع الكتلة لليمين واليسار
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Column(
              // هذه هي الكتلة التي ستكون على اليمين
              crossAxisAlignment:
                  pw.CrossAxisAlignment.end, // محاذاة كل المحتوى لليمين
              children: [
                // معلومات المتجر بالعربية
                pw.Text(
                  companyName,
                  style: pw.TextStyle(
                    fontSize: 24,
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont(companyName, isBold: true),
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyType,
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _getAppropriateFont(companyType),
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyAddress,
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _getAppropriateFont(companyAddress),
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.Text(
                  companyPhones.map((p) => '📞 $p').join(' - '),
                  style: pw.TextStyle(
                    fontSize: 12,
                    font: _getAppropriateFont(companyPhones.join(' - ')),
                  ), // هنا سيتم تطبيق Fallback على رمز 📞
                  textDirection: pw.TextDirection.rtl,
                ),

                pw.SizedBox(height: 10), // فاصل بين اللغتين
                // معلومات المتجر بالإنجليزية
                pw.Text(
                  'Osama Market',
                  style: pw.TextStyle(
                    fontSize: 18,
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont('Osama Market', isBold: true),
                  ),
                  textDirection: pw.TextDirection.ltr, // اتجاه لليسار
                ),
                pw.Text(
                  'For Food Supplies',
                  style: pw.TextStyle(
                    fontSize: 10,
                    font: _getAppropriateFont(),
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.Text(
                  'Al-Masoudi Station - Main Street',
                  style: pw.TextStyle(
                    fontSize: 10,
                    font: _getAppropriateFont(),
                  ),
                  textDirection: pw.TextDirection.ltr,
                ),
                pw.Text(
                  companyPhones.map((p) => '📞 $p').join(' - '),
                  style: pw.TextStyle(
                    fontSize: 10,
                    font: _getAppropriateFont(),
                  ), // هنا سيتم تطبيق Fallback على رمز 📞
                  textDirection: pw.TextDirection.ltr,
                ),
              ],
            ),
            // لا يوجد Expanded هنا لدفعها لليسار. Column واحد يكفي.
            // هذا الجزء يدفع كل شيء في الصف إلى أقصى اليمين.
            // إذا أردت مساحة بيضاء على اليسار، استخدم pw.Spacer() هنا
            // أو إذا كانت هناك عناصر أخرى على اليسار، ضعها في عمود منفصل.
            // بناءً على النموذج: لا يوجد شيء على اليسار، فقط هذه الكتلة على اليمين.
            pw.Expanded(
              child: pw.SizedBox(),
            ), // لدفع الكتلة العربية لليمين (لضمان ألا تمتد على كامل العرض)
          ],
        ),
        pw.SizedBox(height: 15),
        pw.Divider(height: 1, color: PdfColors.grey), // خط فاصل
        pw.SizedBox(height: 15),

        // الصف الثاني: عنوان الفاتورة وتصنيف الدفع
        pw.Column(
          children: [
            pw.Text(
              invoiceTitle, // فاتورة مبيعات / مشتريات
              style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.Text(
              paymentType, // نقدي / آجل / مدفوعة جزئياً
              style: const pw.TextStyle(fontSize: 14),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
        pw.SizedBox(height: 15),

        // الصف الثالث: اسم العميل/المورد والتاريخ ورقم الفاتورة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            // التاريخ ورقم الفاتورة (يسار)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start, // محاذاة لليسار
              children: [
                pw.RichText(
                  text: pw.TextSpan(
                    children: [
                      pw.TextSpan(
                        text: 'التاريخ: ',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      pw.TextSpan(
                        text: _dateFormat.format(invoiceDate),
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  textDirection:
                      pw.TextDirection.rtl, // اتجاه لليمين للنص العربي
                ),
                pw.RichText(
                  text: pw.TextSpan(
                    children: [
                      pw.TextSpan(
                        text: 'رقم الفاتورة: ',
                        style: pw.TextStyle(
                          fontWeight: pw.FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                      pw.TextSpan(
                        text: invoiceNumber,
                        style: const pw.TextStyle(fontSize: 12),
                      ),
                    ],
                  ),
                  textDirection:
                      pw.TextDirection.rtl, // اتجاه لليمين للنص العربي
                ),
              ],
            ),

            // اسم العميل/المورد (يمين)
            pw.RichText(
              text: pw.TextSpan(
                children: [
                  pw.TextSpan(
                    text: '$entityTypeLabel: ',
                    style: pw.TextStyle(
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  pw.TextSpan(
                    text: entityName,
                    style: const pw.TextStyle(fontSize: 12),
                  ),
                ],
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
      ],
    );
  }

  /// بناء جدول العناصر الجديد للمبيعات - يطابق النموذج المرفق
  static pw.Widget _buildSaleItemsTableV2(List<SaleItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.2), // القيمة
        1: const pw.FlexColumnWidth(1.2), // السعر
        2: const pw.FlexColumnWidth(1), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.5), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2('القيمة', isHeader: true),
            _buildTableCellV2('السعر', isHeader: true),
            _buildTableCellV2('الكمية', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('م', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null ? ' ${item.unit}' : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(_currencyFormat.format(item.totalPrice)),
              _buildTableCellV2(_currencyFormat.format(item.unitPrice)),
              _buildTableCellV2('${item.quantity ?? 1}$unitText'),
              _buildTableCellV2(
                item.description ?? 'Product ${item.productId}',
              ),
              _buildTableCellV2(index.toString()),
            ],
          );
        }),
        // إزالة الصفوف الفارغة - الجدول ديناميكي بحسب عدد الأصناف
      ],
    );
  }

  /// بناء خلية الجدول الجديدة
  static pw.Widget _buildTableCellV2(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
        textDirection: pw.TextDirection.rtl,
      ),
    );
  }

  /// بناء إجماليات المبيعات الجديدة - يطابق النموذج المرفق
  static pw.Widget _buildSaleTotalsV2(Sale sale) {
    return pw.Column(
      children: [
        pw.SizedBox(height: 16),

        // الإجمالي محاذاة لليمين
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.symmetric(vertical: 12),
          child: pw.Align(
            alignment: pw.Alignment.centerRight,
            child: pw.Text(
              'الإجمالي: ${_currencyFormat.format(sale.totalAmount - sale.discountAmount)}',
              style: pw.TextStyle(
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                font: _getAppropriateFont(
                  'الإجمالي: ${_currencyFormat.format(sale.totalAmount - sale.discountAmount)}',
                  isBold: true,
                ),
              ),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
        ),

        pw.SizedBox(height: 20),

        // قسم الملاحظات
        pw.Container(
          width: double.infinity,
          padding: const pw.EdgeInsets.all(12),
          decoration: pw.BoxDecoration(
            border: pw.Border.all(color: PdfColors.grey400),
          ),
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'ملاحظات:',
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  font: _getAppropriateFont('ملاحظات:', isBold: true),
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                '- التأكد من استلام جميع بنود الفاتورة',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _getAppropriateFont(
                    '- التأكد من استلام جميع بنود الفاتورة',
                  ),
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'شكراً لتعاملكم معنا',
                style: pw.TextStyle(
                  fontSize: 10,
                  font: _getAppropriateFont('شكراً لتعاملكم معنا'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء صف الإجمالي الجديد
  static pw.Widget _buildTotalRowV2(
    String label,
    double amount, {
    bool isFinal = false,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
              font: _getAppropriateFont(label, isBold: isFinal),
            ),
          ),
          pw.Text(
            _currencyFormat.format(amount),
            style: pw.TextStyle(
              fontSize: isFinal ? 12 : 11,
              fontWeight: isFinal ? pw.FontWeight.bold : pw.FontWeight.normal,
              font: _getAppropriateFont(
                _currencyFormat.format(amount),
                isBold: isFinal,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء تذييل الفاتورة الجديد
  static pw.Widget _buildInvoiceFooterV2() {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      child: pw.Align(
        alignment: pw.Alignment.centerRight,
        child: pw.Text(
          'شكراً لتعاملكم معنا',
          style: pw.TextStyle(
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            font: _getAppropriateFont('شكراً لتعاملكم معنا', isBold: true),
          ),
          textDirection: pw.TextDirection.rtl,
        ),
      ),
    );
  }

  /// بناء جدول العناصر الجديد للمشتريات
  static pw.Widget _buildPurchaseItemsTableV2(List<PurchaseItem> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey400),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.8), // القيمة
        1: const pw.FlexColumnWidth(1.5), // السعر
        2: const pw.FlexColumnWidth(1.2), // الكمية
        3: const pw.FlexColumnWidth(3), // البيان
        4: const pw.FlexColumnWidth(0.8), // م
      },
      children: [
        // رأس الجدول (ترتيب من اليمين لليسار)
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCellV2('القيمة', isHeader: true),
            _buildTableCellV2('السعر', isHeader: true),
            _buildTableCellV2('الكمية', isHeader: true),
            _buildTableCellV2('البيان', isHeader: true),
            _buildTableCellV2('م', isHeader: true),
          ],
        ),

        // صفوف البيانات
        ...items.asMap().entries.map((entry) {
          final index = entry.key + 1;
          final item = entry.value;
          final unitText = item.unit != null ? ' ${item.unit}' : '';

          return pw.TableRow(
            children: [
              _buildTableCellV2(_currencyFormat.format(item.totalPrice)),
              _buildTableCellV2(_currencyFormat.format(item.unitPrice)),
              _buildTableCellV2('${item.quantity}$unitText'),
              _buildTableCellV2('منتج ${item.productId}'), // يمكن تحسينه لاحقاً
              _buildTableCellV2(index.toString()),
            ],
          );
        }),
        // إزالة الصفوف الفارغة - الجدول ديناميكي بحسب عدد الأصناف
      ],
    );
  }

  /// بناء إجماليات المشتريات الجديدة
  static pw.Widget _buildPurchaseTotalsV2(Purchase purchase) {
    return pw.Row(
      children: [
        // الملاحظات (يمين)
        pw.Expanded(
          flex: 2,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.end, // محاذاة لليمين
              children: [
                pw.Text(
                  'ملاحظات:',
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    font: _getAppropriateFont('ملاحظات:', isBold: true),
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
                pw.SizedBox(height: 20),
                pw.Text(''),
              ],
            ),
          ),
        ),

        pw.SizedBox(width: 8),

        // الإجماليات (يسار)
        pw.Expanded(
          flex: 1,
          child: pw.Container(
            padding: const pw.EdgeInsets.all(12),
            decoration: pw.BoxDecoration(
              border: pw.Border.all(color: PdfColors.grey400),
            ),
            child: pw.Column(
              children: [
                _buildTotalRowV2('الإجمالي:', purchase.totalAmount),
                if (purchase.discountAmount > 0)
                  _buildTotalRowV2('الخصم:', purchase.discountAmount),
                _buildTotalRowV2(
                  'الصافي:',
                  purchase.totalAmount - purchase.discountAmount,
                  isFinal: true,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
