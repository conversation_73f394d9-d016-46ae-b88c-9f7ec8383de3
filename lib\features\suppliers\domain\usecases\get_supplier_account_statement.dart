import '../entities/supplier_account.dart';
import '../repositories/supplier_account_repository.dart';

class GetSupplierAccountStatementUseCase {
  final SupplierAccountRepository _repository;

  GetSupplierAccountStatementUseCase(this._repository);

  Future<List<SupplierAccountStatementItem>> call(int supplierId) async {
    print(
      '🔍 DEBUG: GetSupplierAccountStatementUseCase called for supplierId: $supplierId',
    );

    if (supplierId <= 0) {
      throw Exception('Supplier ID must be greater than 0');
    }

    try {
      // Get all supplier account entries
      print('🔍 DEBUG: Calling repository.getSupplierAccountEntries...');
      final entries = await _repository.getSupplierAccountEntries(supplierId);
      print('📊 DEBUG: Repository returned ${entries.length} entries');

      // Calculate running balance and convert to statement items
      // Since entries are ordered DESC (newest first), we need to calculate balance from oldest to newest
      // First, reverse the list to calculate running balance correctly
      final reversedEntries = entries.reversed.toList();

      double runningBalance = 0.0;
      final statementItems = <SupplierAccountStatementItem>[];

      for (final entry in reversedEntries) {
        print(
          '🔍 DEBUG: Processing entry: id=${entry.id}, type=${entry.type}, amount=${entry.amount}, date=${entry.transactionDate}',
        );

        double debit = 0.0;
        double credit = 0.0;

        if (entry.type == 'purchase_invoice') {
          // Purchase invoice increases what we owe (debit)
          debit = entry.amount;
          runningBalance += entry.amount;
          print(
            '📊 DEBUG: Purchase invoice - debit: $debit, new runningBalance: $runningBalance',
          );
        } else if (entry.type == 'payment_out') {
          // Payment decreases what we owe (credit)
          credit = entry.amount;
          runningBalance -= entry.amount;
          print(
            '📊 DEBUG: Payment out - credit: $credit, new runningBalance: $runningBalance',
          );
        }

        final statementItem = SupplierAccountStatementItem(
          date: entry.transactionDate,
          description: entry.description ?? 'معاملة ${entry.type}',
          debit: debit,
          credit: credit,
          balance: runningBalance,
        );

        print(
          '📋 DEBUG: Created statement item: debit=${statementItem.debit}, credit=${statementItem.credit}, balance=${statementItem.balance}',
        );

        statementItems.add(statementItem);
      }

      // Reverse back to show newest first
      return statementItems.reversed.toList();
    } catch (e) {
      throw Exception('Failed to get supplier account statement: $e');
    }
  }
}
