import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/sale.dart';
import '../../domain/entities/sale_item.dart';
import '../../domain/usecases/create_sale.dart';
import '../../domain/usecases/get_all_sales.dart';
import '../../domain/usecases/get_sale_by_id.dart';
import '../../domain/usecases/update_sale.dart';
import '../../domain/usecases/delete_sale.dart';
import '../../domain/repositories/sale_repository.dart';
import '../../../products/presentation/providers/product_provider.dart';
import '../../../customers/presentation/providers/customer_provider.dart';
import '../../../customers/domain/entities/customer_account.dart';
import '../../../activities/presentation/providers/activity_provider.dart';

class SaleProvider extends ChangeNotifier {
  final CreateSaleUseCase _createSaleUseCase;
  final GetAllSalesUseCase _getAllSalesUseCase;
  final GetSaleByIdUseCase _getSaleByIdUseCase;
  final UpdateSaleUseCase _updateSaleUseCase;
  final DeleteSaleUseCase _deleteSaleUseCase;
  final SaleRepository _saleRepository;

  SaleProvider()
    : _createSaleUseCase = GetIt.instance<CreateSaleUseCase>(),
      _getAllSalesUseCase = GetIt.instance<GetAllSalesUseCase>(),
      _getSaleByIdUseCase = GetIt.instance<GetSaleByIdUseCase>(),
      _updateSaleUseCase = GetIt.instance<UpdateSaleUseCase>(),
      _deleteSaleUseCase = GetIt.instance<DeleteSaleUseCase>(),
      _saleRepository = GetIt.instance<SaleRepository>();

  // State variables
  List<Sale> _sales = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Filter variables
  String? _selectedPaymentStatus;
  String? _selectedPaymentMethod;
  int? _selectedCustomerId;
  DateTime? _selectedFromDate;
  DateTime? _selectedToDate;

  // Getters
  List<Sale> get sales => _sales;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get selectedPaymentStatus => _selectedPaymentStatus;
  String? get selectedPaymentMethod => _selectedPaymentMethod;
  int? get selectedCustomerId => _selectedCustomerId;
  DateTime? get selectedFromDate => _selectedFromDate;
  DateTime? get selectedToDate => _selectedToDate;

  // Fetch all sales
  Future<void> fetchSales() async {
    _setLoading(true);
    _clearError();

    try {
      _sales = await _getAllSalesUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load sales: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Create sale with atomic transaction
  Future<bool> createSale(
    Sale sale,
    List<SaleItem> items, {
    double? paidAmount,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      // Get required providers
      final productProvider = GetIt.instance<ProductProvider>();
      final customerProvider = GetIt.instance<CustomerProvider>();
      final activityProvider = GetIt.instance<ActivityProvider>();

      // *** جديد: حساب تكلفة البضاعة المباعة باستخدام FIFO ***
      double totalCalculatedCostOfGoodsSold = 0.0;

      for (final item in items) {
        if (item.isWholesaleProduct &&
            item.productId != null &&
            item.quantity != null) {
          // حساب التكلفة لكل عنصر باستخدام FIFO
          final itemCost = await productProvider.getCostForQuantity(
            item.productId!,
            item.quantity!,
          );
          totalCalculatedCostOfGoodsSold += itemCost;

          print(
            '🔍 DEBUG: تكلفة المنتج ${item.productId}: ${itemCost.toStringAsFixed(2)} ر.ي للكمية ${item.quantity}',
          );
        }
      }

      // Calculate total amount from items
      final calculatedTotalAmount = items.fold<double>(
        0.0,
        (sum, item) => sum + item.totalPrice,
      );

      // حساب صافي المبلغ بعد الخصم
      final netAmount = calculatedTotalAmount - sale.discountAmount;

      // *** جديد: حساب الربح ***
      final calculatedProfit = netAmount - totalCalculatedCostOfGoodsSold;

      // Use provided paidAmount or default to sale's totalPaidAmount
      final actualPaidAmount = paidAmount ?? sale.totalPaidAmount;

      // Calculate status based on payment (استخدم netAmount بدلاً من calculatedTotalAmount)
      String calculatedStatus;
      if (actualPaidAmount >= netAmount) {
        calculatedStatus = 'completed';
      } else if (actualPaidAmount > 0) {
        calculatedStatus = 'partially_paid';
      } else {
        calculatedStatus = 'pending';
      }

      print(
        '🔍 DEBUG: حساب الأرباح - إجمالي المبلغ: ${calculatedTotalAmount.toStringAsFixed(2)}, صافي المبلغ: ${netAmount.toStringAsFixed(2)}, تكلفة البضاعة: ${totalCalculatedCostOfGoodsSold.toStringAsFixed(2)}, الربح: ${calculatedProfit.toStringAsFixed(2)}',
      );

      // Create updated sale object with calculated values
      final updatedSale = sale.copyWith(
        totalAmount: calculatedTotalAmount,
        totalPaidAmount: actualPaidAmount,
        costOfGoodsSold: totalCalculatedCostOfGoodsSold, // *** جديد ***
        profit: calculatedProfit, // *** جديد ***
        status: calculatedStatus,
      );

      print(
        '🔍 DEBUG: تم إنشاء عملية بيع (بعد النسخ): customerId=${updatedSale.customerId}, totalAmount=${updatedSale.totalAmount}, paidAmount=${updatedSale.totalPaidAmount}, dueAmount=${updatedSale.dueAmount}, paymentMethod=${updatedSale.paymentMethod}, status=${updatedSale.status}',
      );

      // Create sale using use case (this handles the database transaction)
      final saleId = await _createSaleUseCase.call(updatedSale, items);

      // Update product quantities and batches for wholesale products
      for (final item in items) {
        if (item.isWholesaleProduct &&
            item.productId != null &&
            item.quantity != null) {
          await productProvider.updateProductQuantitiesForSalesAndPurchases(
            item.productId!,
            item.quantity!,
            isDecrease: true, // Decrease for sale
          );
        }
      }

      // Add customer account entries for registered customers (only if there's due amount)
      print(
        '🔍 DEBUG: محاولة إضافة قيد حساب العميل. التحقق من الشرط: customerId!=null (${updatedSale.customerId != null}) && dueAmount>0 (${updatedSale.dueAmount > 0})',
      );
      if (updatedSale.customerId != null && updatedSale.dueAmount > 0) {
        print(
          '🔍 DEBUG: الشرط تحقق: جاري محاولة إضافة قيد حساب العميل للمبيعات. المبلغ المستحق: ${updatedSale.dueAmount}',
        );
        await customerProvider.addCustomerAccountEntry(
          CustomerAccount(
            customerId: updatedSale.customerId!,
            transactionDate: updatedSale.saleDate,
            type: 'sale_invoice',
            amount: updatedSale.dueAmount, // المبلغ المستحق فقط
            description: 'فاتورة مبيعات رقم #$saleId',
            relatedInvoiceId: saleId,
            isPaid: false,
          ),
        );
        print('🔍 DEBUG: تم استدعاء addCustomerAccountEntry للمبيعات بنجاح.');
      } else {
        print(
          '🔍 DEBUG: لم يتم إضافة قيد حساب العميل للمبيعات. السبب: customerId غير موجود أو dueAmount<=0. (CustomerId: ${updatedSale.customerId}, DueAmount: ${updatedSale.dueAmount})',
        );
      }

      // Refresh sales list
      await fetchSales();

      // Refresh activities
      await activityProvider.refreshActivities();

      return true;
    } catch (e) {
      // تحسين رسائل الخطأ باللغة العربية
      String errorMessage = 'فشل في إنشاء فاتورة المبيعات';
      if (e.toString().contains('Insufficient')) {
        errorMessage = 'مخزون غير كافي لأحد المنتجات';
      } else if (e.toString().contains('Customer')) {
        errorMessage = 'خطأ في بيانات العميل';
      } else if (e.toString().contains('Payment')) {
        errorMessage = 'خطأ في بيانات الدفع';
      } else if (e.toString().contains('Database')) {
        errorMessage = 'خطأ في قاعدة البيانات';
      }
      _setError('$errorMessage: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale
  Future<bool> updateSale(Sale sale) async {
    _setLoading(true);
    _clearError();

    try {
      await _updateSaleUseCase.call(sale);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update sale with items
  Future<bool> updateSaleWithItems(Sale sale, List<SaleItem> items) async {
    _setLoading(true);
    _clearError();

    try {
      await _saleRepository.updateSaleWithItems(sale, items);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to update sale with items: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete sale
  Future<bool> deleteSale(int id) async {
    _setLoading(true);
    _clearError();

    try {
      await _deleteSaleUseCase.call(id);
      await fetchSales(); // Refresh list
      return true;
    } catch (e) {
      _setError('Failed to delete sale: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get sale by ID
  Future<Sale?> getSaleById(int id) async {
    try {
      return await _getSaleByIdUseCase.call(id);
    } catch (e) {
      _setError('Failed to get sale: ${e.toString()}');
      return null;
    }
  }

  // Get sale items
  Future<List<SaleItem>> getSaleItems(int saleId) async {
    try {
      return await _saleRepository.getSaleItems(saleId);
    } catch (e) {
      _setError('Failed to get sale items: ${e.toString()}');
      return [];
    }
  }

  // Get sales by customer
  Future<List<Sale>> getSalesByCustomer(int customerId) async {
    try {
      return await _saleRepository.getSalesByCustomer(customerId);
    } catch (e) {
      _setError('Failed to get sales by customer: ${e.toString()}');
      return [];
    }
  }

  // Get sales by status
  List<Sale> getSalesByStatus(String status) {
    return _sales.where((sale) => sale.status == status).toList();
  }

  // Get sales by payment method
  List<Sale> getSalesByPaymentMethod(String paymentMethod) {
    return _sales.where((sale) => sale.paymentMethod == paymentMethod).toList();
  }

  // Get sales statistics
  Future<Map<String, dynamic>> getSalesStatistics() async {
    try {
      return await _saleRepository.getSalesStatistics();
    } catch (e) {
      _setError('Failed to get sales statistics: ${e.toString()}');
      return {};
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Clear sales list
  void clearSales() {
    _sales.clear();
    notifyListeners();
  }

  // Search sales
  List<Sale> searchSales(String query) {
    if (query.isEmpty) return _sales;

    return _sales.where((sale) {
      final saleId = sale.id?.toString() ?? '';
      final customerId = sale.customerId?.toString() ?? '';
      final searchQuery = query.toLowerCase();

      return saleId.contains(searchQuery) || customerId.contains(searchQuery);
    }).toList();
  }

  // Filter sales
  void filterSales({
    String? paymentStatus,
    String? paymentMethod,
    int? customerId,
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    _selectedPaymentStatus = paymentStatus;
    _selectedPaymentMethod = paymentMethod;
    _selectedCustomerId = customerId;
    _selectedFromDate = fromDate;
    _selectedToDate = toDate;
    notifyListeners();
  }

  // Get filtered sales (by all filters and search)
  List<Sale> getFilteredSales(String searchQuery) {
    List<Sale> filteredSales = _sales;

    // Apply payment status filter
    if (_selectedPaymentStatus != null && _selectedPaymentStatus!.isNotEmpty) {
      switch (_selectedPaymentStatus) {
        case 'مدفوعة بالكامل':
          filteredSales = filteredSales
              .where((sale) => sale.isFullyPaid)
              .toList();
          break;
        case 'مدفوعة جزئياً':
          filteredSales = filteredSales
              .where((sale) => sale.isPartiallyPaid)
              .toList();
          break;
        case 'غير مدفوعة':
          filteredSales = filteredSales.where((sale) => sale.isUnpaid).toList();
          break;
      }
    }

    // Apply payment method filter
    if (_selectedPaymentMethod != null && _selectedPaymentMethod!.isNotEmpty) {
      filteredSales = filteredSales
          .where((sale) => sale.paymentMethod == _selectedPaymentMethod)
          .toList();
    }

    // Apply customer filter
    if (_selectedCustomerId != null) {
      filteredSales = filteredSales
          .where((sale) => sale.customerId == _selectedCustomerId)
          .toList();
    }

    // Apply date range filter
    if (_selectedFromDate != null) {
      filteredSales = filteredSales
          .where(
            (sale) =>
                sale.saleDate.isAfter(_selectedFromDate!) ||
                sale.saleDate.isAtSameMomentAs(_selectedFromDate!),
          )
          .toList();
    }
    if (_selectedToDate != null) {
      final endOfDay = DateTime(
        _selectedToDate!.year,
        _selectedToDate!.month,
        _selectedToDate!.day,
        23,
        59,
        59,
      );
      filteredSales = filteredSales
          .where(
            (sale) =>
                sale.saleDate.isBefore(endOfDay) ||
                sale.saleDate.isAtSameMomentAs(endOfDay),
          )
          .toList();
    }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredSales = filteredSales.where((sale) {
        final saleId = sale.id?.toString() ?? '';
        final customerId = sale.customerId?.toString() ?? '';
        final searchQueryLower = searchQuery.toLowerCase();

        return saleId.contains(searchQueryLower) ||
            customerId.contains(searchQueryLower);
      }).toList();
    }

    return filteredSales;
  }

  // Clear all filters
  void clearFilters() {
    _selectedPaymentStatus = null;
    _selectedPaymentMethod = null;
    _selectedCustomerId = null;
    _selectedFromDate = null;
    _selectedToDate = null;
    notifyListeners();
  }
}
