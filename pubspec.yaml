name: market
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management, navigation, database, dependency injection, localization, and utilities
  provider: ^6.1.2
  go_router: ^14.0.0
  sqflite: ^2.3.3
  sqflite_common_ffi_web: ^0.4.0
  get_it: ^7.7.0
  intl: ^0.20.2
  path_provider: ^2.1.3
  permission_handler: ^12.0.0+1
  path: ^1.9.1

  # Excel and file handling
  excel: ^2.0.0
  file_picker: ^8.0.0+1

  # PDF generation and printing
  pdf: ^3.11.1
  printing: ^5.13.2

  # Contacts access
  flutter_contacts: ^1.1.9

  # Google Drive API for cloud backup
  googleapis: ^12.0.0
  googleapis_auth: ^1.4.1

  # Google Fonts removed - using system fonts for better offline support

  # Background tasks for automatic backup (disabled temporarily due to compatibility issues)
  # workmanager: ^0.5.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Testing dependencies
  mockito: ^5.4.6
  build_runner: ^2.4.15
  sqflite_common_ffi: ^2.3.3

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/fonts/noto_arabic/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: NotoSansArabic
      fonts:
        - asset: assets/fonts/noto_arabic/NotoSansArabic-Regular.ttf
        - asset: assets/fonts/noto_arabic/NotoSansArabic-Bold.ttf
          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
